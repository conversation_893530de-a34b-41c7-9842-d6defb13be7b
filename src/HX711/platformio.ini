; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[platformio]
src_dir = examples/HX711_basic_example
include_dir = .

[config]
build_flags =
    -D VERSION=0.7.1
    -D DEBUG=1

src_filter =
    +<*>
    +<../../*.cpp>


[env:feather_328]
platform = atmelavr
framework = arduino
board = feather328p

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:atmega_2560]
platform = atmelavr
framework = arduino
board = megaatmega2560

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:huzzah]
platform = espressif8266
framework = arduino
board = huzzah

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:lopy4]
platform = espressif32
framework = arduino
board = lopy4

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:teensy31]
platform = teensy
framework = arduino
board = teensy31

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:teensy36]
platform = teensy
framework = arduino
board = teensy36

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:feather_m0]
platform = atmelsam
framework = arduino
board = adafruit_feather_m0

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:arduino_due]
platform = atmelsam
framework = arduino
board = dueUSB

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:feather_m4]
platform = atmelsam
framework = arduino
board = adafruit_feather_m4

; Build options
build_flags = ${config.build_flags}
src_filter = ${config.src_filter}


[env:bluepill]
platform = ststm32
framework = arduino
board = bluepill_f103c8

; Build options
;build_flags = ${config.build_flags}
src_filter = ${config.src_filter}
