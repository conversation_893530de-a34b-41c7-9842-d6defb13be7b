{"name": "HX711", "keywords": "hx711, scale, weight", "description": "An Arduino library to interface the Avia Semiconductor HX711 24-Bit Analog-to-Digital Converter (ADC) for Weight Scales.", "repository": {"type": "git", "url": "https://github.com/bogde/HX711.git"}, "version": "0.7.1", "exclude": "tests", "examples": "examples/*/*.ino", "frameworks": "a<PERSON><PERSON><PERSON>", "platforms": ["atmelavr", "espressif8266", "espressif32", "atmelsam", "ststm32"]}