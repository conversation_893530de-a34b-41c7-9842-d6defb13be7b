name=HX711 Arduino Library
version=0.7.3
author=<PERSON><PERSON><PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>
maintainer=<PERSON><PERSON><PERSON> <<EMAIL>>
sentence=Library to interface the Avia Semiconductor HX711 ADC.
paragraph=An Arduino library to interface the <a href="http://image.dfrobot.com/image/data/SEN0160/hx711_english.pdf">Avia Semiconductor HX711 24-Bit Analog-to-Digital Converter (ADC)</a> for reading load cells / weight scales.
category=Sensors
url=https://github.com/bogde/HX711
