# HX711 library contributors

Listed in the order of appearance.

- <PERSON><PERSON>: First steps
- <PERSON><PERSON><PERSON>: Making it real
- <PERSON>: Performance improvements on AVR. Simplify read logic.
- <PERSON>: Support to read the current `get_offset` and `get_scale`
- <PERSON>: Move pin definition out of constructor
- <PERSON>: Improve documentation
- <PERSON>-<PERSON>: Improve interrupt safety on AVR
- <PERSON><PERSON> et al.: ESP32 support
- <PERSON>: Support for Teensy 3.2 and non-blocking readings
- <PERSON>: Improve ESP8266 stability
- <PERSON>: Spring cleaning, multiarch support
- <PERSON><PERSON><PERSON>: Hardware testing
- Many bits and pieces by countless people from the community,
  see also "doc/backlog.rst" in the repository.

Thanks a bunch!
