#include <Arduino.h>

/**
 * "Skull Bowl Potion Making" Additive Weight Puzzle
 *
 * Players must first place a skull bowl to activate the puzzle, then add
 * ingredients to make a potion in the correct sequence.
 *
 * Rules:
 * 1. Place skull bowl first to activate puzzle (turns on light)
 * 2. Add ingredients in the specified order (weights are cumulative)
 * 3. Can add next ingredient immediately after previous one
 * 4. Failure only occurs after 4 ingredients have been added
 * 5. On failure, remove ALL ingredients (not skull) to reset
 * 6. Removing and replacing skull resets EVERYTHING (even if solved)
 *
 * Success: Skull + 4 correct ingredients → unlock
 * Failure: Skull + 4 wrong ingredients → failure signal
 *
 * Configuration: Modify skull weight and potionIngredients array.
 */

// DEFINES
// Flag to toggle debugging output
#define DEBUG
// Calibration mode to read measurements when the scale is first set up
#define CALIBRATE

// INCLUDES
// Library for interfacing with the HX711 24bit ADC module based on https://github.com/bogde/HX711
#include "./HX711/src/HX711.h"
// Allows calibration data to be stored on the Arudino EEPROM
#include <EEPROM.h>

// CONSTANTS
// Simple signature in first byte of EEPROM indicates stored data is valid
const byte eepromSignature = 100;
// Pin which will be driven HIGH when the puzzle is solved
const byte lockPin = 2;
// Pin which will be driven HIGH when there's a failure
const byte failPin = 8;
// Pin which will be driven HIGH when skull bowl is detected
const byte lightPin = 9;
const byte dataPin = 3;
const byte clockPin = 4;

// SKULL BOWL CONFIGURATION - MODIFY HERE TO CHANGE SKULL BOWL WEIGHT
const float SKULL_BOWL_WEIGHT = 100.0f;    // Expected weight of skull bowl (grams)
const float SKULL_BOWL_TOLERANCE = 10.0f;  // Acceptable weight variance (grams)

// POTION INGREDIENT STRUCTURE - Easy to modify recipe configuration
struct PotionIngredient {
  float weight;          // Weight of this ingredient alone (grams)
  float tolerance;       // Acceptable weight variance (grams)
  const char* name;      // Name for debugging/display
  float cumulativeWeight; // Expected total weight after adding this ingredient (INCLUDING skull bowl)
};

// POTION RECIPE CONFIGURATION - MODIFY HERE TO CHANGE THE RECIPE
// Note: cumulative weights include the skull bowl (100g base weight)
const int NUM_INGREDIENTS = 4;
PotionIngredient potionRecipe[NUM_INGREDIENTS] = {
  {30.0f,  5.0f, "Dragon Scale",    130.0f},  // Ingredient 1: 30g, total = 100g (skull) + 30g = 130g
  {25.0f,  4.0f, "Phoenix Feather", 155.0f},  // Ingredient 2: 25g, total = 130g + 25g = 155g
  {20.0f,  3.0f, "Unicorn Hair",    175.0f},  // Ingredient 3: 20g, total = 155g + 20g = 175g
  {15.0f,  3.0f, "Fairy Dust",      190.0f}   // Ingredient 4: 15g, total = 175g + 15g = 190g
};

// GLOBALS
// Initialise the load cell module, parameters are Data(Out), Clock pin
HX711 scale;
// The correct scaleFactor value will be determined by running with the CALIBRATE flag set
float scaleFactor = -95.5f;

// SKULL BOWL AND POTION MAKING STATE TRACKING
bool skullBowlPresent = false;          // Is the skull bowl currently detected?
bool skullBowlWasPresent = false;       // Was skull bowl present in previous reading?
bool puzzleActive = false;              // Is the puzzle currently active (skull bowl placed)?
int currentIngredient = 0;              // Which ingredient we're waiting for (0 to NUM_INGREDIENTS-1)
int ingredientsAdded = 0;               // How many ingredients have been added so far
bool potionComplete = false;           // Has the entire recipe been completed?
bool puzzleFailed = false;             // Has there been a failure?
unsigned long lastWeightTime = 0;      // Last time we took a weight reading
const unsigned long WEIGHT_INTERVAL = 500; // How often to check weight (ms)
const float EMPTY_THRESHOLD = 5.0f;    // Weight below this is considered "empty" (grams)

// Track state of overall puzzle
enum PuzzleState {Initialising, Running, Solved};
PuzzleState puzzleState = Initialising;

// Loads stored calibration data from EEPROM
void loadCalibration(){
  // Check that the EEPROM starts with a valid signature
  if (EEPROM.read(0) == eepromSignature){
    // Load the calibration data
    EEPROM.get(1, scaleFactor);
    #ifdef DEBUG
      Serial.print("Calibration data loaded from EEPROM. Scale factor set to ");
      Serial.println(scaleFactor);
    #endif
  }
  else {
    #ifdef DEBUG
      Serial.println("No valid calibration data found");
    #endif
  } 
}
     
// Saves calibration data to EEPROM
void saveCalibration(){
  // Erase the existing signature
  EEPROM.write(0, 0);
  // Write the pattern starting from first byte
  EEPROM.put(1, scaleFactor);
  // Now write the signature back again to byte zero to show valid data
  EEPROM.write(0, eepromSignature);
  #ifdef DEBUG
    Serial.println("Calibration data saved to EEPROM");
  #endif
}

// This method is called when the puzzle is solved
void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(1000);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
  // The puzzle has now been solved
  puzzleState = Solved;
}

// Helper function to signal failure
void triggerFailure() {
  puzzleFailed = true;
  digitalWrite(failPin, HIGH);
  Serial.println(F("\n❌ WRONG INGREDIENT! Potion ruined!"));
  Serial.println(F("Remove ALL ingredients (keep skull bowl) and start over..."));
}

// Helper function to reset the potion recipe (ingredients only, not skull bowl)
void resetPotion() {
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  puzzleFailed = false;
  digitalWrite(failPin, LOW);
  Serial.println(F("\n🔄 Ingredients removed! Ready to brew again..."));
  Serial.print(F("Add first ingredient: "));
  Serial.println(potionRecipe[0].name);
}

// Helper function to activate the puzzle when skull bowl is placed
void activatePuzzle() {
  puzzleActive = true;
  digitalWrite(lightPin, HIGH);
  Serial.println(F("\n💀 SKULL BOWL DETECTED! Puzzle activated!"));
  Serial.println(F("🧪 POTION MAKING PUZZLE 🧪"));
  Serial.println(F("Add ingredients in this exact order:"));
  for (int i = 0; i < NUM_INGREDIENTS; i++) {
    Serial.print(F("  "));
    Serial.print(i + 1);
    Serial.print(F(". "));
    Serial.print(potionRecipe[i].name);
    Serial.print(F(" (~"));
    Serial.print(potionRecipe[i].weight);
    Serial.print(F("g)"));
    if (i == 0) {
      Serial.print(F(" → Total: "));
      Serial.print(potionRecipe[i].cumulativeWeight);
      Serial.print(F("g"));
    }
    Serial.println();
  }
  Serial.println();
  Serial.print(F("Ready! Add first ingredient: "));
  Serial.println(potionRecipe[0].name);
}

// Helper function to deactivate the puzzle when skull bowl is removed
void deactivatePuzzle() {
  puzzleActive = false;
  potionComplete = false;
  puzzleFailed = false;
  currentIngredient = 0;
  ingredientsAdded = 0;
  digitalWrite(lightPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lockPin, LOW);
  Serial.println(F("\n💀 SKULL BOWL REMOVED! Puzzle deactivated and reset."));
  Serial.println(F("Place skull bowl to start puzzle..."));
}

void setup() {
  // The serial monitor is required for debugging and calibration
  // #if defined(DEBUG) || defined(CALIBRATE)
    Serial.begin(115200);
    // Wait for serial port to connect (important for ESP32-C3 USB CDC)
    while (!Serial && millis() < 5000) {
      delay(10);
    }
    Serial.println(F("=== ESP32-C3 Weight Puzzle Starting ==="));
    Serial.println(F("Serial communication test successful!"));
    Serial.print(F("Chip model: "));
    Serial.println(ESP.getChipModel());
    Serial.print(F("Free heap: "));
    Serial.println(ESP.getFreeHeap());
    Serial.println(F("Initializing HX711..."));
  // #endif

  // Start the connection to the HX711 module on specified Data / Clock pins
  scale.begin(dataPin, clockPin);
  
  // The steps required to calibrate the scale are also described at https://github.com/bogde/HX711
  #ifdef CALIBRATE

    Serial.println(F("Calibrating..."));

    // 1. Set scale to 1.0
    scale.set_scale();
    
    // 2. Calculate the unladen (tare) weight when there is
    // no load, which will be used to offset subsequent readings.
    scale.tare();
  
    // 3. Obtain reading of known weight
    Serial.println(F("Place a known weight on the scale"));
    Serial.println(F("And type its mass in grams into the Arduino IDE Serial Monitor"));
    
    // Wait until the user types the mass over the serial input
    while (!Serial.available()){ ; }
    // Read the user input and store the weight
    int knownWeight = Serial.parseInt();
    // Calculate an average reading from the ADC for the known weight
    float reading = scale.get_units(10);
    
    // 4. Calculate scale factor from the reading of the known weight
    scaleFactor = reading / (float)knownWeight;
    Serial.print(F("Scale factor calculated: ")); 
    Serial.println(scaleFactor, 1);

    // 5. Save the scale factor to EEPROM
    saveCalibration();

    // Calibration complete
    Serial.println(F("Now remove the weight"));
    Serial.println(F("and send 'y' to continue"));
    
    // Wait until 'y' input is sent over the serial input before continuing
    while (Serial.read() != (unsigned char)'y') {;}

    Serial.println(F("Calibration complete. You may now remove #define CALIBRATE code line")); 

  // If we're not in calibration mode
  #else
    // Load the calibrated scale factor from EEPROM
    loadCalibration();
  #endif
  
  // Set the scale based on the calculated/stored scale factor
  scale.set_scale(scaleFactor);
  // Reset the weight reading when nothing present
  scale.tare();  
  
  // Initialise the output pins
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);
  pinMode(lightPin, OUTPUT);
  digitalWrite(lockPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lightPin, LOW);

  // Initialize skull bowl and potion making state
  skullBowlPresent = false;
  skullBowlWasPresent = false;
  puzzleActive = false;
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  puzzleFailed = false;
  lastWeightTime = 0;

  // Display potion recipe instructions
  Serial.println(F("\n� POTION MAKING PUZZLE �"));
  Serial.print(F("Place skull bowl (~"));
  Serial.print(SKULL_BOWL_WEIGHT);
  Serial.println(F("g) to activate the puzzle..."));
  Serial.println(F("The light will turn on when skull bowl is detected."));

  puzzleState = Running;
}

// Main program loop
void loop() {

  // Action to take depends on the current state of the puzzle
  switch(puzzleState) {

    case Running:
      {
        // Only check weight at specified intervals
        if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
          lastWeightTime = millis();

          // Take an average of 5 readings from the scale
          float currentWeight = scale.get_units(5);

          // First, check for skull bowl presence
          skullBowlWasPresent = skullBowlPresent;
          skullBowlPresent = abs(currentWeight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE;

          // Handle skull bowl state changes
          if (skullBowlPresent && !skullBowlWasPresent) {
            // Skull bowl just placed
            activatePuzzle();
          } else if (!skullBowlPresent && skullBowlWasPresent) {
            // Skull bowl just removed
            deactivatePuzzle();
            return; // Don't process further logic this cycle
          }

          // Only process puzzle logic if skull bowl is present and puzzle is active
          if (!puzzleActive || !skullBowlPresent) {
            #ifdef DEBUG
              Serial.print(F("Waiting for skull bowl... Current weight: "));
              Serial.print(currentWeight);
              Serial.println(F("g"));
            #endif
            return;
          }

          // Check if we're in a failed state and waiting for ingredient reset
          if (puzzleFailed) {
            // Check if ingredients have been removed (weight back to just skull bowl)
            if (abs(currentWeight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE) {
              // Ingredients removed - reset the puzzle
              resetPotion();
            } else {
              #ifdef DEBUG
                Serial.print(F("Failed state - Current weight: "));
                Serial.print(currentWeight);
                Serial.print(F("g (waiting for skull bowl only: "));
                Serial.print(SKULL_BOWL_WEIGHT);
                Serial.println(F("g)"));
              #endif
            }
            return; // Don't process normal logic while in failed state
          }

          // Check if we're still working on the potion recipe
          if (!potionComplete) {

            #ifdef DEBUG
              Serial.print(F("Ingredients added: "));
              Serial.print(ingredientsAdded);
              Serial.print(F("/"));
              Serial.print(NUM_INGREDIENTS);
              Serial.print(F(" | Current weight: "));
              Serial.print(currentWeight);
              Serial.print(F("g"));
              if (currentIngredient < NUM_INGREDIENTS) {
                Serial.print(F(" | Waiting for: "));
                Serial.print(potionRecipe[currentIngredient].name);
                Serial.print(F(" (total should be "));
                Serial.print(potionRecipe[currentIngredient].cumulativeWeight);
                Serial.print(F("g)"));
              }
              Serial.println();
            #endif

            // Check if we have exactly 4 ingredients added
            if (ingredientsAdded == NUM_INGREDIENTS) {
              // Check if the final weight matches the complete recipe
              float finalExpectedWeight = potionRecipe[NUM_INGREDIENTS-1].cumulativeWeight;
              if (abs(currentWeight - finalExpectedWeight) <= potionRecipe[NUM_INGREDIENTS-1].tolerance) {
                // Success! All ingredients correct
                potionComplete = true;
                Serial.println(F("POTION COMPLETE! The magical brew is ready!"));
                onSolve();
              } else {
                // Failure! 4 ingredients but wrong total weight
                triggerFailure();
              }
            } else if (currentIngredient < NUM_INGREDIENTS) {
              // Still adding ingredients - check if current weight matches expected cumulative weight
              PotionIngredient& ingredient = potionRecipe[currentIngredient];
              bool weightMatches = abs(currentWeight - ingredient.cumulativeWeight) <= ingredient.tolerance;

              if (weightMatches && ingredientsAdded == currentIngredient) {
                // Correct ingredient added!
                ingredientsAdded++;
                currentIngredient++;
                Serial.print(F("✓ "));
                Serial.print(ingredient.name);
                Serial.println(F(" added correctly!"));

                if (currentIngredient < NUM_INGREDIENTS) {
                  Serial.print(F("Next ingredient: Add "));
                  Serial.println(potionRecipe[currentIngredient].name);
                } else {
                  Serial.println(F("All ingredients added! Checking final recipe..."));
                }
              } else if (currentWeight > potionRecipe[currentIngredient > 0 ? currentIngredient-1 : 0].cumulativeWeight + 2.0f) {
                // Something was added but weight doesn't match - count as an ingredient added
                if (ingredientsAdded < currentIngredient + 1) {
                  ingredientsAdded++;
                  Serial.print(F("⚠ Ingredient "));
                  Serial.print(ingredientsAdded);
                  Serial.println(F(" added (checking if correct...)"));

                  // If we've now added 4 ingredients, the failure check will happen on next loop
                }
              }
            }
          }
        }
      }
      break;

    case Solved:
      break;
  }
}
