#include <Arduino.h>

/**
 * "Weight For It!" Sequential Object Puzzle
 *
 * To solve this puzzle, players must place specific objects onto a
 * container/hook in a specific sequence. Each object must:
 * 1. Match the expected weight (within tolerance)
 * 2. Be held steady for the minimum required time
 * 3. Be placed in the correct order
 *
 * If an object is removed before the time requirement is met,
 * or if the wrong object is placed, the sequence resets.
 *
 * When all objects are placed correctly in sequence,
 * a relay will be activated to release a maglock.
 *
 * Configuration: Modify the puzzleSequence array to change
 * the number of objects, their weights, tolerances, and timing.
 */

// DEFINES
// Flag to toggle debugging output
#define DEBUG
// Calibration mode to read measurements when the scale is first set up
// #define CALIBRATE

// INCLUDES
// Library for interfacing with the HX711 24bit ADC module based on https://github.com/bogde/HX711
#include "./HX711/src/HX711.h"
// Allows calibration data to be stored on the Arudino EEPROM
#include <EEPROM.h>

// CONSTANTS
// Simple signature in first byte of EEPROM indicates stored data is valid
const byte eepromSignature = 100;
// Pin which will be driven HIGH when the puzzle is solved
const byte lockPin = 2;
const byte dataPin = 3;
const byte clockPin = 4;

// PUZZLE OBJECT STRUCTURE - Easy to modify puzzle configuration
struct PuzzleObject {
  float weight;        // Expected weight in grams
  float tolerance;     // Acceptable weight variance in grams
  const char* name;    // Name for debugging/display
  unsigned long minTime; // Minimum time object must be present (ms)
};

// PUZZLE CONFIGURATION - MODIFY HERE TO CHANGE THE PUZZLE
const int NUM_OBJECTS = 4;
PuzzleObject puzzleSequence[NUM_OBJECTS] = {
  {50.0f,  5.0f, "Skull",     3000},  // Object 1: ~50g, ±5g, 3 seconds
  {100.0f, 8.0f, "Potion",    3000},  // Object 2: ~100g, ±8g, 3 seconds
  {75.0f,  6.0f, "Crystal",   3000},  // Object 3: ~75g, ±6g, 3 seconds
  {25.0f,  4.0f, "Key",       3000}   // Object 4: ~25g, ±4g, 3 seconds
};

// GLOBALS
// Initialise the load cell module, parameters are Data(Out), Clock pin
HX711 scale;
// The correct scaleFactor value will be determined by running with the CALIBRATE flag set
float scaleFactor = -95.5f;

// PUZZLE STATE TRACKING
int currentStep = 0;                    // Which object we're waiting for (0 to NUM_OBJECTS-1)
unsigned long stepStartTime = 0;       // When current object was first detected
bool objectDetected = false;           // Is current object on scale?
bool puzzleSolved = false;             // Has the entire sequence been completed?
unsigned long lastWeightTime = 0;      // Last time we took a weight reading
const unsigned long WEIGHT_INTERVAL = 500; // How often to check weight (ms)

// Track state of overall puzzle
enum PuzzleState {Initialising, Running, Solved};
PuzzleState puzzleState = Initialising;

// Loads stored calibration data from EEPROM
void loadCalibration(){
  // Check that the EEPROM starts with a valid signature
  if (EEPROM.read(0) == eepromSignature){
    // Load the calibration data
    EEPROM.get(1, scaleFactor);
    #ifdef DEBUG
      Serial.print("Calibration data loaded from EEPROM. Scale factor set to ");
      Serial.println(scaleFactor);
    #endif
  }
  else {
    #ifdef DEBUG
      Serial.println("No valid calibration data found");
    #endif
  } 
}
     
// Saves calibration data to EEPROM
void saveCalibration(){
  // Erase the existing signature
  EEPROM.write(0, 0);
  // Write the pattern starting from first byte
  EEPROM.put(1, scaleFactor);
  // Now write the signature back again to byte zero to show valid data
  EEPROM.write(0, eepromSignature);
  #ifdef DEBUG
    Serial.println("Calibration data saved to EEPROM");
  #endif
}

// This method is called when the puzzle is solved
void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(1000);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
  // The puzzle has now been solved
  puzzleState = Solved;
}

// Helper function to reset the puzzle sequence
void resetPuzzle() {
  currentStep = 0;
  objectDetected = false;
  puzzleSolved = false;
  Serial.println(F("\n🔄 Puzzle reset! Starting over..."));
  Serial.print(F("Place "));
  Serial.println(puzzleSequence[0].name);
}

void setup() {
  // The serial monitor is required for debugging and calibration
  // #if defined(DEBUG) || defined(CALIBRATE)
    Serial.begin(115200);
    // Wait for serial port to connect (important for ESP32-C3 USB CDC)
    while (!Serial && millis() < 5000) {
      delay(10);
    }
    Serial.println(F("=== ESP32-C3 Weight Puzzle Starting ==="));
    Serial.println(F("Serial communication test successful!"));
    Serial.print(F("Chip model: "));
    Serial.println(ESP.getChipModel());
    Serial.print(F("Free heap: "));
    Serial.println(ESP.getFreeHeap());
    Serial.println(F("Initializing HX711..."));
  // #endif

  // Start the connection to the HX711 module on specified Data / Clock pins
  scale.begin(dataPin, clockPin);
  
  // The steps required to calibrate the scale are also described at https://github.com/bogde/HX711
  #ifdef CALIBRATE

    Serial.println(F("Calibrating..."));

    // 1. Set scale to 1.0
    scale.set_scale();
    
    // 2. Calculate the unladen (tare) weight when there is
    // no load, which will be used to offset subsequent readings.
    scale.tare();
  
    // 3. Obtain reading of known weight
    Serial.println(F("Place a known weight on the scale"));
    Serial.println(F("And type its mass in grams into the Arduino IDE Serial Monitor"));
    
    // Wait until the user types the mass over the serial input
    while (!Serial.available()){ ; }
    // Read the user input and store the weight
    int knownWeight = Serial.parseInt();
    // Calculate an average reading from the ADC for the known weight
    float reading = scale.get_units(10);
    
    // 4. Calculate scale factor from the reading of the known weight
    scaleFactor = reading / (float)knownWeight;
    Serial.print(F("Scale factor calculated: ")); 
    Serial.println(scaleFactor, 1);

    // 5. Save the scale factor to EEPROM
    saveCalibration();

    // Calibration complete
    Serial.println(F("Now remove the weight"));
    Serial.println(F("and send 'y' to continue"));
    
    // Wait until 'y' input is sent over the serial input before continuing
    while (Serial.read() != (unsigned char)'y') {;}

    Serial.println(F("Calibration complete. You may now remove #define CALIBRATE code line")); 

  // If we're not in calibration mode
  #else
    // Load the calibrated scale factor from EEPROM
    loadCalibration();
  #endif
  
  // Set the scale based on the calculated/stored scale factor
  scale.set_scale(scaleFactor);
  // Reset the weight reading when nothing present
  scale.tare();  
  
  // Initialise the relay pin
  pinMode(lockPin, OUTPUT);
  digitalWrite(lockPin, LOW);

  // Initialize puzzle sequence
  currentStep = 0;
  objectDetected = false;
  puzzleSolved = false;
  lastWeightTime = 0;

  // Display puzzle instructions
  Serial.println(F("\n🧩 SEQUENTIAL OBJECT PUZZLE 🧩"));
  Serial.println(F("Place the following objects in order:"));
  for (int i = 0; i < NUM_OBJECTS; i++) {
    Serial.print(F("  "));
    Serial.print(i + 1);
    Serial.print(F(". "));
    Serial.print(puzzleSequence[i].name);
    Serial.print(F(" (~"));
    Serial.print(puzzleSequence[i].weight);
    Serial.print(F("g, hold for "));
    Serial.print(puzzleSequence[i].minTime / 1000.0);
    Serial.println(F(" seconds)"));
  }
  Serial.println();
  Serial.print(F("Ready! Place "));
  Serial.println(puzzleSequence[0].name);

  puzzleState = Running;
}

// Main program loop
void loop() {

  // Action to take depends on the current state of the puzzle
  switch(puzzleState) {

    case Running:
      {
        // Only check weight at specified intervals
        if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
          lastWeightTime = millis();

          // Take an average of 5 readings from the scale
          float currentWeight = scale.get_units(5);

          // Check if we're still working on the puzzle sequence
          if (currentStep < NUM_OBJECTS) {
            PuzzleObject& currentObject = puzzleSequence[currentStep];

            // Check if current object weight matches expected weight
            bool weightMatches = abs(currentWeight - currentObject.weight) <= currentObject.tolerance;

            #ifdef DEBUG
              Serial.print(F("Step "));
              Serial.print(currentStep + 1);
              Serial.print(F("/"));
              Serial.print(NUM_OBJECTS);
              Serial.print(F(" - Waiting for: "));
              Serial.print(currentObject.name);
              Serial.print(F(" ("));
              Serial.print(currentObject.weight);
              Serial.print(F("g ± "));
              Serial.print(currentObject.tolerance);
              Serial.print(F("g) | Current: "));
              Serial.print(currentWeight);
              Serial.print(F("g"));

              if (weightMatches) {
                unsigned long timeHeld = millis() - stepStartTime;
                Serial.print(F(" | DETECTED! Time held: "));
                Serial.print(timeHeld);
                Serial.print(F("ms / "));
                Serial.print(currentObject.minTime);
                Serial.print(F("ms required"));
              }
              Serial.println();
            #endif

            if (weightMatches) {
              if (!objectDetected) {
                // Object just detected - start timing
                objectDetected = true;
                stepStartTime = millis();
                Serial.print(F("✓ "));
                Serial.print(currentObject.name);
                Serial.println(F(" detected! Hold steady..."));
              } else {
                // Object still detected - check if held long enough
                unsigned long timeHeld = millis() - stepStartTime;
                if (timeHeld >= currentObject.minTime) {
                  // Object held long enough - advance to next step
                  Serial.print(F("✓ "));
                  Serial.print(currentObject.name);
                  Serial.println(F(" confirmed!"));

                  currentStep++;
                  objectDetected = false;

                  if (currentStep >= NUM_OBJECTS) {
                    // All objects completed!
                    Serial.println(F("🎉 PUZZLE SOLVED! All objects placed in correct order!"));
                    onSolve();
                  } else {
                    // Move to next object
                    Serial.print(F("Next: Place "));
                    Serial.println(puzzleSequence[currentStep].name);
                  }
                }
              }
            } else {
              if (objectDetected) {
                // Object was detected but now removed/changed - reset current step
                Serial.print(F("✗ "));
                Serial.print(currentObject.name);
                Serial.println(F(" removed too early! Restarting sequence..."));
                currentStep = 0;
                objectDetected = false;
                Serial.print(F("Place "));
                Serial.println(puzzleSequence[0].name);
              }
            }
          }
        }
      }
      break;

    case Solved:
      break;
  }
}
