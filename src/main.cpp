#include <Arduino.h>

/**
 * "Skull Bowl Potion Making" Additive Weight Puzzle
 *
 * Players must first place a skull bowl to activate the puzzle, then add
 * ingredients to make a potion in the correct sequence.
 *
 * Rules:
 * 1. Place skull bowl first to activate puzzle (turns on light)
 * 2. Add ingredients in the specified order (weights are cumulative)
 * 3. Can add next ingredient immediately after previous one
 * 4. Failure only occurs after 4 ingredients have been added
 * 5. On failure, remove ALL ingredients (not skull) to reset
 * 6. Removing and replacing skull resets EVERYTHING (even if solved)
 *
 * Success: Skull + 4 correct ingredients → unlock
 * Failure: Skull + 4 wrong ingredients → failure signal
 *
 * Configuration: Modify skull weight and potionIngredients array.
 */

// DEFINES
// Flag to toggle debugging output
#define DEBUG
// Calibration mode to read measurements when the scale is first set up
#define CALIBRATE

// INCLUDES
// Library for interfacing with the HX711 24bit ADC module based on https://github.com/bogde/HX711
#include "./HX711/src/HX711.h"
// Allows calibration data to be stored on the Arudino EEPROM
#include <EEPROM.h>

// CONSTANTS
// Simple signature in first byte of EEPROM indicates stored data is valid
const byte eepromSignature = 100;
// Pin which will be driven HIGH when the puzzle is solved
const byte lockPin = 2;
// Pin which will be driven HIGH when there's a failure
const byte failPin = 10;
// Pin which will be driven HIGH when skull bowl is detected
const byte lightPin = 9;
const byte dataPin = 3;
const byte clockPin = 4;

// SKULL BOWL CONFIGURATION - MODIFY HERE TO CHANGE SKULL BOWL WEIGHT
const float SKULL_BOWL_WEIGHT = 100.0f;    // Expected weight of skull bowl (grams)
const float SKULL_BOWL_TOLERANCE = 10.0f;  // Acceptable weight variance (grams)

// POTION INGREDIENT STRUCTURE - Easy to modify recipe configuration
struct PotionIngredient {
  float weight;          // Weight of this ingredient alone (grams)
  float tolerance;       // Acceptable weight variance (grams)
  const char* name;      // Name for debugging/display
  float cumulativeWeight; // Expected total weight after adding this ingredient (INCLUDING skull bowl)
};

// POTION RECIPE CONFIGURATION - MODIFY HERE TO CHANGE THE RECIPE
// Note: cumulative weights include the skull bowl (100g base weight)
const int NUM_INGREDIENTS = 4;
PotionIngredient potionRecipe[NUM_INGREDIENTS] = {
  {15.0f,  5.0f, "Dragon Scale",    130.0f},  // Ingredient 1: 30g, total = 100g (skull) + 30g = 130g
  {30.0f,  4.0f, "Phoenix Feather", 155.0f},  // Ingredient 2: 25g, total = 130g + 25g = 155g
  {50.0f,  3.0f, "Unicorn Hair",    175.0f},  // Ingredient 3: 20g, total = 155g + 20g = 175g
  {143.0f,  3.0f, "Fairy Dust",      190.0f}   // Ingredient 4: 15g, total = 175g + 15g = 190g
};

// GLOBALS
// Initialise the load cell module, parameters are Data(Out), Clock pin
HX711 scale;
// Scale factor for converting ADC readings to gramsy
float scaleFactor = -44.2f;

// SKULL BOWL AND POTION MAKING STATE TRACKING
bool skullBowlPresent = false;          // Is the skull bowl currently detected?
bool skullBowlWasPresent = false;       // Was skull bowl present in previous reading?
bool puzzleActive = false;              // Is the puzzle currently active (skull bowl placed)?
int currentIngredient = 0;              // Which ingredient we're waiting for (0 to NUM_INGREDIENTS-1)
int ingredientsAdded = 0;               // How many ingredients have been added so far
bool potionComplete = false;           // Has the entire recipe been completed?
bool hasFailed = false;                // Has there been a failure?
unsigned long lastWeightTime = 0;      // Last time we took a weight reading
const unsigned long WEIGHT_INTERVAL = 500; // How often to check weight (ms)
const float EMPTY_THRESHOLD = 5.0f;    // Weight below this is considered "empty" (grams)

// SKULL BOWL DETECTION STABILITY
int skullBowlDetectionCount = 0;        // Consecutive readings detecting skull bowl
int skullBowlAbsenceCount = 0;          // Consecutive readings NOT detecting skull bowl
const int DETECTION_STABILITY_THRESHOLD = 3; // Require 3 consecutive readings to change state

// PUZZLE STATE MACHINE
enum PuzzleState {
  Calibrating,
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;

unsigned long lastStateChange = 0;
unsigned long failIndicatorDuration = 3000;  // How long to show failure before reset
unsigned long lockDuration = 500;            // How long to pulse lock signal

// FUNCTION PROTOTYPES
void loadCalibration();
void saveCalibration();
void updateState(PuzzleState newState);
void startPuzzle();

void puzzleSolved();
void triggerPuzzleFailure();
void deactivatePuzzle();
void resetPotion();
bool detectSkullBowl(float weight);
void processIngredients(float weight);
void calibrate();

// SETUP FUNCTION
void setup() {
  // Initialize serial communication
  Serial.begin(115200);
  while (!Serial) { ; } // Wait for serial port to connect

  // Initialize the scale
  scale.begin(dataPin, clockPin);

  // Load calibration from EEPROM or start calibration
  #ifdef CALIBRATE
    puzzleState = Calibrating;
  #else
    loadCalibration();
  #endif

  // Set the scale based on the calculated/stored scale factor
  scale.set_scale(scaleFactor);
  // Reset the weight reading when nothing present
  scale.tare();

  // Initialise the output pins
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);
  pinMode(lightPin, OUTPUT);
  digitalWrite(lockPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lightPin, LOW);

  // Initialize skull bowl and potion making state
  skullBowlPresent = false;
  skullBowlWasPresent = false;
  puzzleActive = false;
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  hasFailed = false;
  lastWeightTime = 0;
  skullBowlDetectionCount = 0;
  skullBowlAbsenceCount = 0;

  updateState(Inactive);
}

void loop() {
  // Handle calibration mode
  if (puzzleState == Calibrating) {
    calibrate();
    return;
  }

  // Get current weight reading
  float currentWeight = 0;
  bool weightValid = false;

  if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
    lastWeightTime = millis();

    if (scale.is_ready()) {
      currentWeight = scale.get_units(3);
      weightValid = true;
    }
  }

  // Detect skull bowl presence with stability
  bool skullDetected = detectSkullBowl(currentWeight);

  // State machine
  switch (puzzleState) {
    case Inactive:
      if (skullDetected) {
        startPuzzle();
      }
      break;

    case InProgress:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (weightValid) {
        processIngredients(currentWeight);
      }
      break;

    case Solved:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > lockDuration) {
        digitalWrite(lockPin, LOW);
      }
      break;

    case Failed:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > failIndicatorDuration) {
        // Reset to ingredients-only state (keep skull bowl)
        resetPotion();
        updateState(InProgress);
      }
      break;
  }


}

// EEPROM FUNCTIONS
void loadCalibration() {
  // Check if there is a valid signature in the first byte of EEPROM
  if (EEPROM.read(0) == eepromSignature) {
    // Read the scale factor from EEPROM
    EEPROM.get(1, scaleFactor);
  } else {
    puzzleState = Calibrating;
  }
}

void saveCalibration() {
  // Write the signature to the first byte of EEPROM
  EEPROM.write(0, eepromSignature);
  // Write the scale factor to EEPROM starting at byte 1
  EEPROM.put(1, scaleFactor);

}

// STATE MANAGEMENT FUNCTIONS
void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void startPuzzle() {
  updateState(InProgress);
  puzzleActive = true;
  digitalWrite(lightPin, HIGH);
}

void puzzleSolved() {
  updateState(Solved);
  digitalWrite(lockPin, HIGH);
}

void triggerPuzzleFailure() {
  updateState(Failed);
  digitalWrite(failPin, HIGH);
}



// Helper function to reset the potion recipe (ingredients only, not skull bowl)
void resetPotion() {
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  hasFailed = false;
  digitalWrite(failPin, LOW);
}

// Helper function to activate the puzzle when skull bowl is placed
void activatePuzzle() {
  puzzleActive = true;
  digitalWrite(lightPin, HIGH);
  Serial.println(F("SKULL BOWL DETECTED! Puzzle activated!"));
}

// Helper function to deactivate the puzzle when skull bowl is removed
void deactivatePuzzle() {
  updateState(Inactive);
  puzzleActive = false;
  potionComplete = false;
  hasFailed = false;
  currentIngredient = 0;
  ingredientsAdded = 0;
  digitalWrite(lightPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lockPin, LOW);
}

// CALIBRATION FUNCTION
void calibrate() {
  // 1. Set scale to 1.0
  scale.set_scale();

  // 2. Calculate the unladen (tare) weight when there is
  // no load, which will be used to offset subsequent readings.
  scale.tare();

  // 3. Obtain reading of known weight
  Serial.println(F("Place known weight and enter mass in grams:"));

  // Wait until the user types the mass over the serial input
  while (!Serial.available()){ ; }
  // Read the user input and store the weight
  int knownWeight = Serial.parseInt();
  // Calculate an average reading from the ADC for the known weight
  float reading = scale.get_units(10);

  // 4. Calculate scale factor from the reading of the known weight
  scaleFactor = reading / (float)knownWeight;
  Serial.print(F("Scale factor calculated: "));
  Serial.println(scaleFactor, 1);

  // 5. Save the scale factor to EEPROM
  saveCalibration();

  // Calibration complete
  Serial.println(F("Now remove the weight"));
  Serial.println(F("and send 'y' to continue"));

  // Wait until 'y' input is sent over the serial input before continuing
  while (Serial.available()) { Serial.read(); } // Clear buffer
  while (!Serial.available()) { ; }
  char input = Serial.read();
  if (input == 'y' || input == 'Y') {
    updateState(Inactive);
  }
}

// HELPER FUNCTIONS
bool detectSkullBowl(float weight) {
  // Check if current reading suggests skull bowl is present
  bool currentReadingDetectsSkull = (abs(weight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE);

  // Update detection counters for stability
  if (currentReadingDetectsSkull) {
    skullBowlDetectionCount++;
    skullBowlAbsenceCount = 0;
  } else {
    skullBowlAbsenceCount++;
    skullBowlDetectionCount = 0;
  }

  // Update skull bowl state only after stable readings
  skullBowlWasPresent = skullBowlPresent;

  // Skull bowl detected: require multiple consecutive detections
  if (!skullBowlPresent && skullBowlDetectionCount >= DETECTION_STABILITY_THRESHOLD) {
    skullBowlPresent = true;
    return true;
  }
  // Skull bowl removed: require multiple consecutive absences
  else if (skullBowlPresent && skullBowlAbsenceCount >= DETECTION_STABILITY_THRESHOLD) {
    skullBowlPresent = false;
    return false;
  }

  return skullBowlPresent;
}

void processIngredients(float weight) {
  // Check if we're back to just skull bowl weight (ingredients removed)
  if (abs(weight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE) {
    // Only skull bowl present - reset potion if we had ingredients before
    if (ingredientsAdded > 0 || hasFailed) {
      resetPotion();
    }
    return;
  }

  // Check if we have the expected cumulative weight for current ingredient
  if (currentIngredient < NUM_INGREDIENTS) {
    float expectedWeight = potionRecipe[currentIngredient].cumulativeWeight;
    float tolerance = potionRecipe[currentIngredient].tolerance;

    if (abs(weight - expectedWeight) <= tolerance) {
      // Correct ingredient added!
      currentIngredient++;
      ingredientsAdded++;

      if (currentIngredient >= NUM_INGREDIENTS) {
        // All ingredients added correctly!
        potionComplete = true;
        puzzleSolved();
      }
    }
  }
  // Check if 4 ingredients have been added but puzzle not solved
  else if (ingredientsAdded >= NUM_INGREDIENTS && !potionComplete && !hasFailed) {
    // Wrong sequence - trigger failure
    triggerPuzzleFailure();
  }
}
