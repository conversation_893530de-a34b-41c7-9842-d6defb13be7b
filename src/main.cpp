#include <Arduino.h>
#include "./HX711/src/HX711.h"
#include <EEPROM.h>

#define CALIBRATE

const byte eepromSignature = 100;
const byte lockPin = 2;
const byte failPin = 10;
const byte lightPin = 9;
const byte dataPin = 3;
const byte clockPin = 4;

const float SKULL_BOWL_WEIGHT = 100.0f;
const float SKULL_BOWL_TOLERANCE = 10.0f;

struct PotionIngredient {
  float weight;
  float tolerance;
  const char* name;
  float cumulativeWeight;
};

const int NUM_INGREDIENTS = 4;
PotionIngredient potionRecipe[NUM_INGREDIENTS] = {
  {15.0f,  5.0f, "Dragon Scale",    130.0f},
  {30.0f,  4.0f, "Phoenix Feather", 155.0f},
  {50.0f,  3.0f, "Unicorn Hair",    175.0f},
  {143.0f,  3.0f, "Fairy Dust",      190.0f},
  
};

HX711 scale;
float scaleFactor = -44.2f;

bool skullBowlPresent = false;
bool skullBowlWasPresent = false;
int currentIngredient = 0;
int ingredientsAdded = 0;
unsigned long lastWeightTime = 0;
const unsigned long WEIGHT_INTERVAL = 500;
const float EMPTY_THRESHOLD = 5.0f;

int skullBowlDetectionCount = 0;
int skullBowlAbsenceCount = 0;
const int DETECTION_STABILITY_THRESHOLD = 3;

enum PuzzleState {
  Calibrating,
  Inactive,
  InProgress,
  Solved,
  Failed
};
PuzzleState puzzleState = Inactive;
unsigned long lastStateChange = 0;
const unsigned long lockDuration = 5000;
const unsigned long failIndicatorDuration = 3000;

void loadCalibration();
void saveCalibration();
void updateState(PuzzleState newState);
void startPuzzle();
void puzzleSolved();
void triggerPuzzleFailure();
void deactivatePuzzle();
void resetPotion();
bool detectSkullBowl(float weight);
void processIngredients(float weight);
void calibrate();

void setup() {
  Serial.begin(115200);
  while (!Serial) { ; }
  scale.begin(dataPin, clockPin);
  #ifdef CALIBRATE
    puzzleState = Calibrating;
  #else
    loadCalibration();
  #endif
  scale.set_scale(scaleFactor);
  scale.tare();
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);
  pinMode(lightPin, OUTPUT);
  digitalWrite(lockPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lightPin, LOW);
  skullBowlPresent = false;
  skullBowlWasPresent = false;
  currentIngredient = 0;
  ingredientsAdded = 0;
  lastWeightTime = 0;
  skullBowlDetectionCount = 0;
  skullBowlAbsenceCount = 0;

  updateState(Inactive);
}

void loop() {
  if (puzzleState == Calibrating) {
    calibrate();
    return;
  }

  float currentWeight = 0;
  bool weightValid = false;

  if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
    lastWeightTime = millis();

    if (scale.is_ready()) {
      currentWeight = scale.get_units(3);
      weightValid = true;
    }
  }

  bool skullDetected = detectSkullBowl(currentWeight);
  switch (puzzleState) {
    case Inactive:
      if (skullDetected) {
        startPuzzle();
      }
      break;

    case InProgress:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (weightValid) {
        processIngredients(currentWeight);
      }
      break;

    case Solved:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > lockDuration) {
        digitalWrite(lockPin, LOW);
      }
      break;

    case Failed:
      if (!skullDetected) {
        deactivatePuzzle();
      } else if (millis() - lastStateChange > failIndicatorDuration) {
        resetPotion();
        updateState(InProgress);
      }
      break;
  }


}

void loadCalibration() {
  if (EEPROM.read(0) == eepromSignature) {
    EEPROM.get(1, scaleFactor);
  } else {
    puzzleState = Calibrating;
  }
}

void saveCalibration() {
  EEPROM.write(0, eepromSignature);
  EEPROM.put(1, scaleFactor);
}
void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void startPuzzle() {
  updateState(InProgress);
  digitalWrite(lightPin, HIGH);
}

void puzzleSolved() {
  updateState(Solved);
  digitalWrite(lockPin, HIGH);
}

void triggerPuzzleFailure() {
  updateState(Failed);
  digitalWrite(failPin, HIGH);
}



// Helper function to reset the potion recipe (ingredients only, not skull bowl)
void resetPotion() {
  currentIngredient = 0;
  ingredientsAdded = 0;
  digitalWrite(failPin, LOW);
}

void deactivatePuzzle() {
  updateState(Inactive);
  currentIngredient = 0;
  ingredientsAdded = 0;
  digitalWrite(lightPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lockPin, LOW);
}

void calibrate() {
  scale.set_scale();
  scale.tare();
  Serial.println(F("Place known weight and enter mass in grams:"));
  while (!Serial.available()){ ; }
  int knownWeight = Serial.parseInt();
  float reading = scale.get_units(10);
  scaleFactor = reading / (float)knownWeight;
  Serial.print(F("Scale factor calculated: "));
  Serial.println(scaleFactor, 1);
  saveCalibration();
  Serial.println(F("Now remove the weight"));
  Serial.println(F("and send 'y' to continue"));
  while (Serial.available()) { Serial.read(); }
  while (!Serial.available()) { ; }
  char input = Serial.read();
  if (input == 'y' || input == 'Y') {
    updateState(Inactive);
  }
}

bool detectSkullBowl(float weight) {
  bool currentReadingDetectsSkull = (abs(weight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE);
  if (currentReadingDetectsSkull) {
    skullBowlDetectionCount++;
    skullBowlAbsenceCount = 0;
  } else {
    skullBowlAbsenceCount++;
    skullBowlDetectionCount = 0;
  }

  skullBowlWasPresent = skullBowlPresent;
  if (!skullBowlPresent && skullBowlDetectionCount >= DETECTION_STABILITY_THRESHOLD) {
    skullBowlPresent = true;
    return true;
  }
  else if (skullBowlPresent && skullBowlAbsenceCount >= DETECTION_STABILITY_THRESHOLD) {
    skullBowlPresent = false;
    return false;
  }
  return skullBowlPresent;
}

void processIngredients(float weight) {
  if (abs(weight - SKULL_BOWL_WEIGHT) <= SKULL_BOWL_TOLERANCE) {
    if (ingredientsAdded > 0 || puzzleState == Failed) {
      resetPotion();
    }
    return;
  }

  if (currentIngredient < NUM_INGREDIENTS) {
    float expectedWeight = potionRecipe[currentIngredient].cumulativeWeight;
    float tolerance = potionRecipe[currentIngredient].tolerance;

    if (abs(weight - expectedWeight) <= tolerance) {
      currentIngredient++;
      ingredientsAdded++;

      if (currentIngredient >= NUM_INGREDIENTS) {
        puzzleSolved();
      }
    }
  }
  else if (ingredientsAdded >= NUM_INGREDIENTS && puzzleState != Solved && puzzleState != Failed) {
    triggerPuzzleFailure();
  }
}
