#include <Arduino.h>
#include "./HX711/src/HX711.h"
#include <EEPROM.h>

#define CALIBRATE

const byte eepromSignature = 100;
const byte lockPin = 2;
const byte dataPin = 3;
const byte clockPin = 4;
const byte redPin = 5;
const byte greenPin = 6;
const byte bluePin = 7;


struct PotionIngredient {
  float weight;
  float tolerance;
  const char* name;
  float cumulativeWeight;
};

const int NUM_INGREDIENTS = 6;
PotionIngredient potionRecipe[NUM_INGREDIENTS] = {
  {100.0f, 10.0f, "Skeleton", 100.0f},
  {30.0f,  4.0f, "Carrot", 130.0f},
  {15.0f,  5.0f, "Apple", 145.0f},
  {15.0f,  5.0f, "Apple", 160.0f},
  {143.0f, 3.0f, "Red Wine", 303.0f},
  {143.0f, 3.0f, "Hem<PERSON>", 446.0f}
};

const float EMPTY_THRESHOLD = 5.0f;

HX711 scale;
float scaleFactor = -44.2f;

int currentIngredient = 0;
int ingredientsAdded = 0;
unsigned long lastWeightTime = 0;
const unsigned long WEIGHT_INTERVAL = 500;

enum PuzzleState {
  Calibrating,
  InProgress,
  Solved
};
PuzzleState puzzleState = InProgress;
unsigned long lastStateChange = 0;
const unsigned long lockDuration = 5000;


void loadCalibration();
void saveCalibration();
void updateState(PuzzleState newState);

void puzzleSolved();
void triggerPuzzleFailure();

void setRGBColor(int red, int green, int blue);
void setLightOff();
void setLightGreen();
void setLightRed();

void processIngredients(float weight);
void calibrate();

void setup() {
  Serial.begin(115200);
  while (!Serial) { ; }
  scale.begin(dataPin, clockPin);
  #ifdef CALIBRATE
    puzzleState = Calibrating;
  #else
    loadCalibration();
  #endif
  scale.set_scale(scaleFactor);
  scale.tare();
  pinMode(lockPin, OUTPUT);
  pinMode(redPin, OUTPUT);
  pinMode(greenPin, OUTPUT);
  pinMode(bluePin, OUTPUT);
  digitalWrite(lockPin, LOW);
  setLightOff();
  currentIngredient = 0;
  ingredientsAdded = 0;
  lastWeightTime = 0;

  updateState(InProgress);
}

void loop() {
  if (puzzleState == Calibrating) {
    calibrate();
    return;
  }

  float currentWeight = 0;
  bool weightValid = false;

  if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
    lastWeightTime = millis();

    if (scale.is_ready()) {
      currentWeight = scale.get_units(3);
      weightValid = true;
    }
  }

  switch (puzzleState) {
    case InProgress:
      if (weightValid) {
        processIngredients(currentWeight);
      }
      break;

    case Solved:
      if (millis() - lastStateChange > lockDuration) {
        digitalWrite(lockPin, LOW);
      }
      break;

  }
}

void loadCalibration() {
  if (EEPROM.read(0) == eepromSignature) {
    EEPROM.get(1, scaleFactor);
  } else {
    puzzleState = Calibrating;
  }
}

void saveCalibration() {
  EEPROM.write(0, eepromSignature);
  EEPROM.put(1, scaleFactor);
}
void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}



void puzzleSolved() {
  updateState(Solved);
  digitalWrite(lockPin, HIGH);
  setLightOff();
}

void triggerPuzzleFailure() {
  setLightRed();
}

void setLightOff() {
  setRGBColor(0, 0, 0);
}

void setLightGreen() {
  setRGBColor(0, 255, 0);
}

void setLightRed() {
  setRGBColor(255, 0, 0);
}



// Helper function to reset the potion recipe (ingredients only, not skull bowl)




void calibrate() {
  scale.set_scale();
  scale.tare();
  Serial.println(F("Place known weight and enter mass in grams:"));
  while (!Serial.available()){ ; }
  int knownWeight = Serial.parseInt();
  float reading = scale.get_units(10);
  scaleFactor = reading / (float)knownWeight;
  Serial.print(F("Scale factor calculated: "));
  Serial.println(scaleFactor, 1);
  saveCalibration();
  Serial.println(F("Now remove the weight"));
  Serial.println(F("and send 'y' to continue"));
  while (Serial.available()) { Serial.read(); }
  while (!Serial.available()) { ; }
  char input = Serial.read();
  if (input == 'y' || input == 'Y') {
    updateState(InProgress);
  }
}



void processIngredients(float weight) {
  if (weight <= EMPTY_THRESHOLD) {
    if (ingredientsAdded > 0) {
      currentIngredient = 0;
      ingredientsAdded = 0;
    }
    setLightOff();
    return;
  }

  // Check if ingredients were removed (weight decreased)
  for (int i = currentIngredient - 1; i >= 0; i--) {
    float expectedWeight = potionRecipe[i].cumulativeWeight;
    float tolerance = potionRecipe[i].tolerance;

    if (abs(weight - expectedWeight) <= tolerance) {
      // Weight matches a previous step - ingredients were removed
      currentIngredient = i + 1;
      ingredientsAdded = i + 1;
      setLightGreen();
      return;
    }
  }

  // Check if current weight is valid for the current step
  if (currentIngredient < NUM_INGREDIENTS) {
    float expectedWeight = potionRecipe[currentIngredient].cumulativeWeight;
    float tolerance = potionRecipe[currentIngredient].tolerance;

    if (abs(weight - expectedWeight) <= tolerance) {
      // Correct ingredient added
      setLightGreen();
      currentIngredient++;
      ingredientsAdded++;

      if (currentIngredient >= NUM_INGREDIENTS) {
        puzzleSolved();
      } else {
        digitalWrite(lockPin, HIGH);
        delay(200);
        digitalWrite(lockPin, LOW);
      }
    } else {
      // Check if weight matches any previous valid step (show green)
      bool isValidPreviousStep = false;
      for (int i = currentIngredient - 1; i >= 0; i--) {
        float prevWeight = potionRecipe[i].cumulativeWeight;
        float prevTolerance = potionRecipe[i].tolerance;
        if (abs(weight - prevWeight) <= prevTolerance) {
          isValidPreviousStep = true;
          break;
        }
      }

      if (isValidPreviousStep) {
        setLightGreen();
      } else {
        setLightRed();
      }
    }
  } else {
    // Puzzle complete, turn off light
    setLightOff();
  }
}
