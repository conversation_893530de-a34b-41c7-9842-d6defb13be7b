#include <Arduino.h>

/**
 * ESP32-C3 Basic Test - No HX711
 */

// Pin definitions
const byte lockPin = 2;
const byte failPin = 10;
const byte lightPin = 9;

// Simple test counter
int counter = 0;

void setup() {
  Serial.begin(115200);
  delay(2000);

  Serial.println("ESP32-C3 Basic Test Starting...");

  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);
  pinMode(lightPin, OUTPUT);

  digitalWrite(lockPin, LOW);
  digitalWrite(failPin, LOW);
  digitalWrite(lightPin, LOW);

  Serial.println("Setup complete!");
}

void loop() {
  static unsigned long lastPrint = 0;

  if (millis() - lastPrint >= 1000) {
    lastPrint = millis();

    counter++;
    Serial.print("Test ");
    Serial.print(counter);
    Serial.println(" - ESP32-C3 is running!");

    // Test LED blinking
    digitalWrite(lightPin, counter % 2);

    if (counter % 5 == 0) {
      Serial.println("Testing other pins...");
      digitalWrite(lockPin, HIGH);
      digitalWrite(failPin, HIGH);
      delay(100);
      digitalWrite(lockPin, LOW);
      digitalWrite(failPin, LOW);
    }
  }
}
