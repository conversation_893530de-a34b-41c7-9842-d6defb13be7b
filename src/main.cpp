#include <Arduino.h>

/**
 * "Potion Making" Additive Weight Puzzle
 *
 * Players must add ingredients to make a potion in the correct sequence.
 * Each ingredient is added to the container and stays there (additive weights).
 *
 * Rules:
 * 1. Add ingredients in the specified order
 * 2. Ingredients are NOT removed - weights are cumulative
 * 3. Can add next ingredient immediately after previous one
 * 4. Failure only occurs after 4 ingredients have been added
 * 5. On failure, ALL ingredients must be removed to reset
 *
 * Success: All 4 ingredients added correctly → unlock
 * Failure: 4 ingredients added but wrong sequence → failure signal
 *
 * Configuration: Modify the potionIngredients array to change
 * the recipe, weights, and tolerances.
 */

// DEFINES
// Flag to toggle debugging output
#define DEBUG
// Calibration mode to read measurements when the scale is first set up
// #define CALIBRATE

// INCLUDES
// Library for interfacing with the HX711 24bit ADC module based on https://github.com/bogde/HX711
#include "./HX711/src/HX711.h"
// Allows calibration data to be stored on the Arudino EEPROM
#include <EEPROM.h>

// CONSTANTS
// Simple signature in first byte of EEPROM indicates stored data is valid
const byte eepromSignature = 100;
// Pin which will be driven HIGH when the puzzle is solved
const byte lockPin = 2;
// Pin which will be driven HIGH when there's a failure
const byte failPin = 10;
const byte dataPin = 3;
const byte clockPin = 4;

// POTION INGREDIENT STRUCTURE - Easy to modify recipe configuration
struct PotionIngredient {
  float weight;          // Weight of this ingredient alone (grams)
  float tolerance;       // Acceptable weight variance (grams)
  const char* name;      // Name for debugging/display
  float cumulativeWeight; // Expected total weight after adding this ingredient
};

// POTION RECIPE CONFIGURATION - MODIFY HERE TO CHANGE THE RECIPE
const int NUM_INGREDIENTS = 4;
PotionIngredient potionRecipe[NUM_INGREDIENTS] = {
  {30.0f,  5.0f, "Dragon Scale",    3000, 30.0f},   // Ingredient 1: 30g, total = 30g
  {25.0f,  4.0f, "Phoenix Feather", 3000, 55.0f},   // Ingredient 2: 25g, total = 55g
  {20.0f,  3.0f, "Unicorn Hair",    3000, 75.0f},   // Ingredient 3: 20g, total = 75g
  {15.0f,  3.0f, "Fairy Dust",      3000, 90.0f}    // Ingredient 4: 15g, total = 90g
};

// GLOBALS
// Initialise the load cell module, parameters are Data(Out), Clock pin
HX711 scale;
// The correct scaleFactor value will be determined by running with the CALIBRATE flag set
float scaleFactor = -95.5f;

// POTION MAKING STATE TRACKING
int currentIngredient = 0;              // Which ingredient we're waiting for (0 to NUM_INGREDIENTS-1)
int ingredientsAdded = 0;               // How many ingredients have been added so far
bool potionComplete = false;           // Has the entire recipe been completed?
bool puzzleFailed = false;             // Has there been a failure?
unsigned long lastWeightTime = 0;      // Last time we took a weight reading
const unsigned long WEIGHT_INTERVAL = 500; // How often to check weight (ms)
const float EMPTY_THRESHOLD = 5.0f;    // Weight below this is considered "empty" (grams)

// Track state of overall puzzle
enum PuzzleState {Initialising, Running, Solved};
PuzzleState puzzleState = Initialising;

// Loads stored calibration data from EEPROM
void loadCalibration(){
  // Check that the EEPROM starts with a valid signature
  if (EEPROM.read(0) == eepromSignature){
    // Load the calibration data
    EEPROM.get(1, scaleFactor);
    #ifdef DEBUG
      Serial.print("Calibration data loaded from EEPROM. Scale factor set to ");
      Serial.println(scaleFactor);
    #endif
  }
  else {
    #ifdef DEBUG
      Serial.println("No valid calibration data found");
    #endif
  } 
}
     
// Saves calibration data to EEPROM
void saveCalibration(){
  // Erase the existing signature
  EEPROM.write(0, 0);
  // Write the pattern starting from first byte
  EEPROM.put(1, scaleFactor);
  // Now write the signature back again to byte zero to show valid data
  EEPROM.write(0, eepromSignature);
  #ifdef DEBUG
    Serial.println("Calibration data saved to EEPROM");
  #endif
}

// This method is called when the puzzle is solved
void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(1000);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
  // The puzzle has now been solved
  puzzleState = Solved;
}

// Helper function to signal failure
void triggerFailure() {
  puzzleFailed = true;
  digitalWrite(failPin, HIGH);
  Serial.println(F("\n❌ WRONG INGREDIENT! Potion ruined!"));
  Serial.println(F("Remove ALL ingredients and start over..."));
}

// Helper function to reset the potion recipe
void resetPotion() {
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  puzzleFailed = false;
  digitalWrite(failPin, LOW);
  Serial.println(F("\n🔄 Cauldron emptied! Ready to brew again..."));
  Serial.print(F("Add first ingredient: "));
  Serial.println(potionRecipe[0].name);
}

void setup() {
  // The serial monitor is required for debugging and calibration
  // #if defined(DEBUG) || defined(CALIBRATE)
    Serial.begin(115200);
    // Wait for serial port to connect (important for ESP32-C3 USB CDC)
    while (!Serial && millis() < 5000) {
      delay(10);
    }
    Serial.println(F("=== ESP32-C3 Weight Puzzle Starting ==="));
    Serial.println(F("Serial communication test successful!"));
    Serial.print(F("Chip model: "));
    Serial.println(ESP.getChipModel());
    Serial.print(F("Free heap: "));
    Serial.println(ESP.getFreeHeap());
    Serial.println(F("Initializing HX711..."));
  // #endif

  // Start the connection to the HX711 module on specified Data / Clock pins
  scale.begin(dataPin, clockPin);
  
  // The steps required to calibrate the scale are also described at https://github.com/bogde/HX711
  #ifdef CALIBRATE

    Serial.println(F("Calibrating..."));

    // 1. Set scale to 1.0
    scale.set_scale();
    
    // 2. Calculate the unladen (tare) weight when there is
    // no load, which will be used to offset subsequent readings.
    scale.tare();
  
    // 3. Obtain reading of known weight
    Serial.println(F("Place a known weight on the scale"));
    Serial.println(F("And type its mass in grams into the Arduino IDE Serial Monitor"));
    
    // Wait until the user types the mass over the serial input
    while (!Serial.available()){ ; }
    // Read the user input and store the weight
    int knownWeight = Serial.parseInt();
    // Calculate an average reading from the ADC for the known weight
    float reading = scale.get_units(10);
    
    // 4. Calculate scale factor from the reading of the known weight
    scaleFactor = reading / (float)knownWeight;
    Serial.print(F("Scale factor calculated: ")); 
    Serial.println(scaleFactor, 1);

    // 5. Save the scale factor to EEPROM
    saveCalibration();

    // Calibration complete
    Serial.println(F("Now remove the weight"));
    Serial.println(F("and send 'y' to continue"));
    
    // Wait until 'y' input is sent over the serial input before continuing
    while (Serial.read() != (unsigned char)'y') {;}

    Serial.println(F("Calibration complete. You may now remove #define CALIBRATE code line")); 

  // If we're not in calibration mode
  #else
    // Load the calibrated scale factor from EEPROM
    loadCalibration();
  #endif
  
  // Set the scale based on the calculated/stored scale factor
  scale.set_scale(scaleFactor);
  // Reset the weight reading when nothing present
  scale.tare();  
  
  // Initialise the relay and failure pins
  pinMode(lockPin, OUTPUT);
  pinMode(failPin, OUTPUT);
  digitalWrite(lockPin, LOW);
  digitalWrite(failPin, LOW);

  // Initialize potion making state
  currentIngredient = 0;
  ingredientsAdded = 0;
  potionComplete = false;
  puzzleFailed = false;
  lastWeightTime = 0;

  // Display potion recipe instructions
  Serial.println(F("\n� POTION MAKING PUZZLE �"));
  Serial.println(F("Add ingredients in this exact order:"));
  for (int i = 0; i < NUM_INGREDIENTS; i++) {
    Serial.print(F("  "));
    Serial.print(i + 1);
    Serial.print(F(". "));
    Serial.print(potionRecipe[i].name);
    Serial.print(F(" (~"));
    Serial.print(potionRecipe[i].weight);
    Serial.print(F("g) → Total: "));
    Serial.print(potionRecipe[i].cumulativeWeight);
    Serial.println(F("g"));
  }
  Serial.println();
  Serial.print(F("Ready! Add first ingredient: "));
  Serial.println(potionRecipe[0].name);

  puzzleState = Running;
}

// Main program loop
void loop() {

  // Action to take depends on the current state of the puzzle
  switch(puzzleState) {

    case Running:
      {
        // Only check weight at specified intervals
        if (millis() - lastWeightTime >= WEIGHT_INTERVAL) {
          lastWeightTime = millis();

          // Take an average of 5 readings from the scale
          float currentWeight = scale.get_units(5);

          // Check if we're in a failed state and waiting for reset
          if (puzzleFailed) {
            if (currentWeight <= EMPTY_THRESHOLD) {
              // Cauldron has been emptied - reset the puzzle
              resetPotion();
            } else {
              #ifdef DEBUG
                Serial.print(F("Failed state - Current weight: "));
                Serial.print(currentWeight);
                Serial.print(F("g (waiting for empty < "));
                Serial.print(EMPTY_THRESHOLD);
                Serial.println(F("g)"));
              #endif
            }
            return; // Don't process normal logic while in failed state
          }

          // Check if we're still working on the potion recipe
          if (!potionComplete) {

            #ifdef DEBUG
              Serial.print(F("Ingredients added: "));
              Serial.print(ingredientsAdded);
              Serial.print(F("/"));
              Serial.print(NUM_INGREDIENTS);
              Serial.print(F(" | Current weight: "));
              Serial.print(currentWeight);
              Serial.print(F("g"));
              if (currentIngredient < NUM_INGREDIENTS) {
                Serial.print(F(" | Waiting for: "));
                Serial.print(potionRecipe[currentIngredient].name);
                Serial.print(F(" (total should be "));
                Serial.print(potionRecipe[currentIngredient].cumulativeWeight);
                Serial.print(F("g)"));
              }
              Serial.println();
            #endif

            // Check if we have exactly 4 ingredients added
            if (ingredientsAdded == NUM_INGREDIENTS) {
              // Check if the final weight matches the complete recipe
              float finalExpectedWeight = potionRecipe[NUM_INGREDIENTS-1].cumulativeWeight;
              if (abs(currentWeight - finalExpectedWeight) <= potionRecipe[NUM_INGREDIENTS-1].tolerance) {
                // Success! All ingredients correct
                potionComplete = true;
                Serial.println(F("🎉 POTION COMPLETE! The magical brew is ready!"));
                onSolve();
              } else {
                // Failure! 4 ingredients but wrong total weight
                triggerFailure();
              }
            } else if (currentIngredient < NUM_INGREDIENTS) {
              // Still adding ingredients - check if current weight matches expected cumulative weight
              PotionIngredient& ingredient = potionRecipe[currentIngredient];
              bool weightMatches = abs(currentWeight - ingredient.cumulativeWeight) <= ingredient.tolerance;

              if (weightMatches && ingredientsAdded == currentIngredient) {
                // Correct ingredient added!
                ingredientsAdded++;
                currentIngredient++;
                Serial.print(F("✓ "));
                Serial.print(ingredient.name);
                Serial.println(F(" added correctly!"));

                if (currentIngredient < NUM_INGREDIENTS) {
                  Serial.print(F("Next ingredient: Add "));
                  Serial.println(potionRecipe[currentIngredient].name);
                } else {
                  Serial.println(F("All ingredients added! Checking final recipe..."));
                }
              } else if (currentWeight > (currentIngredient > 0 ? potionRecipe[currentIngredient-1].cumulativeWeight + 2.0f : 2.0f)) {
                // Something was added but weight doesn't match - count as an ingredient added
                if (ingredientsAdded < currentIngredient + 1) {
                  ingredientsAdded++;
                  Serial.print(F("⚠ Ingredient "));
                  Serial.print(ingredientsAdded);
                  Serial.println(F(" added (checking if correct...)"));

                  // If we've now added 4 ingredients, the failure check will happen on next loop
                }
              }
            }
          }
        }
      }
      break;

    case Solved:
      break;
  }
}
