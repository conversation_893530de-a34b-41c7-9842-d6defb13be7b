#include <Arduino.h>

/**
 * "Weight For It!" Puzzle
 *
 * To solve this puzzle, players must place item(s) onto a 
 * container/hook which will be attached to a load cell. The load cell
 * will measure the total weight of items placed in the container -
 * When the correct items are placed that match the desired 
 * weight (within a certain tolerance), the puzzle will be 
 * solved and a relay will be activated to release a maglock.
 *
 * Updated 20190914 : New version of HX711 included, and change to
 * serial monitor code to require 'y'
 */

// DEFINES
// Flag to toggle debugging output
#define DEBUG
// Calibration mode to read measurements when the scale is first set up
#define CALIBRATE

// INCLUDES
// Library for interfacing with the HX711 24bit ADC module based on https://github.com/bogde/HX711
#include "./HX711/src/HX711.h"
// Allows calibration data to be stored on the Arudino EEPROM
#include <EEPROM.h>

// CONSTANTS
// Simple signature in first byte of EEPROM indicates stored data is valid
const byte eepromSignature = 100;
// Pin which will be driven HIGH when the puzzle is solved
const byte lockPin = 8;
const byte dataPin = 3;
const byte clockPin = 4;
// The total weight of items which players have to place
const int solutionWeight = 476;
// The allowed difference between the measured weight and
// the desired "solution" weight (in grams).
const int tolerance = 10;

// GLOBALS
// Initialise the load cell module, parameters are Data(Out), Clock pin
HX711 scale;
// The correct scaleFactor value will be determined by running with the CALIBRATE flag set
float scaleFactor = -1315.3f;
// Track state of overall puzzle
enum PuzzleState {Initialising, Running, Solved};
PuzzleState puzzleState = Initialising;

// Loads stored calibration data from EEPROM
void loadCalibration(){
  // Check that the EEPROM starts with a valid signature
  if (EEPROM.read(0) == eepromSignature){
    // Load the calibration data
    EEPROM.get(1, scaleFactor);
    #ifdef DEBUG
      Serial.print("Calibration data loaded from EEPROM. Scale factor set to ");
      Serial.println(scaleFactor);
    #endif
  }
  else {
    #ifdef DEBUG
      Serial.println("No valid calibration data found");
    #endif
  } 
}
     
// Saves calibration data to EEPROM
void saveCalibration(){
  // Erase the existing signature
  EEPROM.write(0, 0);
  // Write the pattern starting from first byte
  EEPROM.put(1, scaleFactor);
  // Now write the signature back again to byte zero to show valid data
  EEPROM.write(0, eepromSignature);
  #ifdef DEBUG
    Serial.println("Calibration data saved to EEPROM");
  #endif
}

// This method is called when the puzzle is solved
void onSolve() {
  // Activate the relay to supply power to the maglock
  digitalWrite(lockPin, HIGH);
  // Wait for one second
  delay(1000);
  // De-activate the relay again
  digitalWrite(lockPin, LOW);
  // The puzzle has now been solved
  puzzleState = Solved;
}

void setup() {
  // The serial monitor is required for debugging and calibration
  // #if defined(DEBUG) || defined(CALIBRATE)
    Serial.begin(115200);
    // Wait for serial port to connect (important for ESP32-C3 USB CDC)
    while (!Serial && millis() < 5000) {
      delay(10);
    }
    Serial.println(F("Serial initialized - ESP32-C3 Weight Puzzle Starting..."));
  // #endif

  // Start the connection to the HX711 module on specified Data / Clock pins
  scale.begin(dataPin, clockPin);
  
  // The steps required to calibrate the scale are also described at https://github.com/bogde/HX711
  #ifdef CALIBRATE

    Serial.println(F("Calibrating..."));

    // 1. Set scale to 1.0
    scale.set_scale();
    
    // 2. Calculate the unladen (tare) weight when there is
    // no load, which will be used to offset subsequent readings.
    scale.tare();
  
    // 3. Obtain reading of known weight
    Serial.println(F("Place a known weight on the scale"));
    Serial.println(F("And type its mass in grams into the Arduino IDE Serial Monitor"));
    
    // Wait until the user types the mass over the serial input
    while (!Serial.available()){ ; }
    // Read the user input and store the weight
    int knownWeight = Serial.parseInt();
    // Calculate an average reading from the ADC for the known weight
    float reading = scale.get_units(10);
    
    // 4. Calculate scale factor from the reading of the known weight
    scaleFactor = reading / (float)knownWeight;
    Serial.print(F("Scale factor calculated: ")); 
    Serial.println(scaleFactor, 1);

    // 5. Save the scale factor to EEPROM
    saveCalibration();

    // Calibration complete
    Serial.println(F("Now remove the weight"));
    Serial.println(F("and send 'y' to continue"));
    
    // Wait until 'y' input is sent over the serial input before continuing
    while (Serial.read() != (unsigned char)'y') {;}

    Serial.println(F("Calibration complete. You may now remove #define CALIBRATE code line")); 

  // If we're not in calibration mode
  #else
    // Load the calibrated scale factor from EEPROM
    loadCalibration();
  #endif
  
  // Set the scale based on the calculated/stored scale factor
  scale.set_scale(scaleFactor);
  // Reset the weight reading when nothing present
  scale.tare();  
  
  // Initialise the relay pin
  pinMode(lockPin, OUTPUT);
  digitalWrite(lockPin, LOW);

  puzzleState = Running;
}

// Main program loop
void loop() {

  // Action to take depends on the current state of the puzzle
  switch(puzzleState) {

    case Running:
      {
      // Take an average of 5 readings from the scale,
      // accounting for tare offset and scale factor
      int value = (int)(scale.get_units(5));

      #ifdef DEBUG
        Serial.print("Reading : ");
        Serial.println(value);
      #endif
  
      // If the reading is within tolerance of the correct value
      if(abs(value - solutionWeight) < tolerance) {
        onSolve();
      }
      delay(500);
      }
      break;

    case Solved:
      break;
  }
}
