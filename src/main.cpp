#include <Arduino.h>
#include "./HX711/src/HX711.h"
#include <EEPROM.h>

#define CALIBRATE

const byte eepromSignature = 100;
const byte lockPin = 2;
const byte dataPin = 3;
const byte clockPin = 4;
const byte redPin = 5;
const byte greenPin = 6;
const byte bluePin = 7;


struct PotionIngredient {
  float weight;
  float tolerance;
  const char* name;
};

PotionIngredient potionRecipe[] = {
  {100.0f, 10.0f, "Skeleton"},
  {30.0f,  4.0f, "Carrot"},
  {15.0f,  5.0f, "Apple"},
  {15.0f,  5.0f, "Apple"},
  {143.0f, 3.0f, "Red Wine"},
  {143.0f, 3.0f, "Hemlock"}
};
const byte expectedNumberOfIngredients = sizeof(potionRecipe) / sizeof(potionRecipe[0]);

const float EMPTY_THRESHOLD = 5.0f;

HX711 scale;
float scaleFactor = -44.2f;

int currentNumberOfIngredients = 0;

enum PuzzleState {
  Initializing,
  Inactive,
  InProgress,
  Failed,
  Solved
};
PuzzleState puzzleState = InProgress;
unsigned long lastStateChange = 0;
const unsigned long lockDuration = 5000;


void loadCalibration();
void updateState(PuzzleState newState);

void puzzleSolved();

void setRGBColor(int red, int green, int blue);
void setLightOff();
void setLightGreen();
void setLightRed();

void processIngredients(float weight);
void calibrate();
void deactivate();
bool isEmpty(float weight);
bool isFirstIngredientOnly(float weight);
void startPuzzle();
void puzzleFailed();
bool weightInRange(int ingredientIndex, float weight);

void setup() {
  Serial.begin(115200);
  while (!Serial) { ; }
  scale.begin(dataPin, clockPin);
  #ifdef CALIBRATE
    calibrate();
  #else
    loadCalibration();
  #endif
  scale.set_scale(scaleFactor);
  scale.tare();
  pinMode(lockPin, OUTPUT);
  digitalWrite(lockPin, LOW);

  // Setup LEDC channels for RGB PWM
  ledcSetup(0, 5000, 8); // Channel 0, 5kHz, 8-bit resolution
  ledcSetup(1, 5000, 8); // Channel 1, 5kHz, 8-bit resolution
  ledcSetup(2, 5000, 8); // Channel 2, 5kHz, 8-bit resolution

  ledcAttachPin(redPin, 0);   // Attach red pin to channel 0
  ledcAttachPin(greenPin, 1); // Attach green pin to channel 1
  ledcAttachPin(bluePin, 2);  // Attach blue pin to channel 2

  
  setLightOff();

  updateState(Inactive);
}

void loop() {
  if (puzzleState == Initializing) { return; }

  if (!scale.is_ready()) return;

  float currentWeight = scale.get_units(5);

  switch (puzzleState) {
    case Inactive:
      if (isFirstIngredientOnly(currentWeight)) {
        startPuzzle();
      } else if (!isEmpty(currentWeight)) {
        puzzleFailed();
      }
    break;
    case InProgress:
      if (isEmpty(currentWeight)) {
        deactivate();
      } else {
        processIngredients(currentWeight);
      }
      break;
    case Solved:
      if (millis() - lastStateChange > lockDuration) {
        digitalWrite(lockPin, LOW);
        if (isEmpty(currentWeight)) {
          deactivate();
        }
      }
      break;
    case Failed:
      if (isEmpty(currentWeight)) {
        deactivate();
      } else if (isFirstIngredientOnly(currentWeight)) {
        startPuzzle();
      }
      break;
  }
}

void loadCalibration() {
  if (EEPROM.read(0) == eepromSignature) {
    EEPROM.get(1, scaleFactor);
    Serial.print("Calibration data loaded from EEPROM. Scale factor set to ");
    Serial.println(scaleFactor);
  } else {
    Serial.println("No calibration data found in EEPROM. ");
  }
}

void calibrate() {
  scale.set_scale();
  scale.tare();
  while (!Serial.available()){ ; }
  Serial.println(F("Place known weight and enter mass in grams:"));
  int knownWeight = Serial.parseInt();
  float reading = scale.get_units(10);
  scaleFactor = reading / (float)knownWeight;
  Serial.print(F("Scale factor calculated: "));
  Serial.println(scaleFactor, 1);
  
  EEPROM.write(0, eepromSignature);
  EEPROM.put(1, scaleFactor);

  Serial.println(F("Now remove the weight"));
  Serial.println(F("and send 'y' to continue"));
  while (Serial.read() != (unsigned char)'y') {;}
}

void processIngredients(float weight) {
  if (isEmpty(weight) || currentNumberOfIngredients == 0) {
    deactivate();
    return;
  }

  if (currentNumberOfIngredients >= expectedNumberOfIngredients) {
    puzzleSolved();
    return;
  }

  if (weightInRange(currentNumberOfIngredients - 1, weight)) {
    return;
  }

  if (weightInRange(currentNumberOfIngredients, weight)) {
    currentNumberOfIngredients++;
    return;
  }

  puzzleFailed();
}

bool weightInRange(int ingredientIndex, float weight) {
  float total = 0.0f;
  for (int i = 0; i <= ingredientIndex; ++i) {
    total += potionRecipe[i].weight;
  }

  float tolerance = potionRecipe[ingredientIndex].tolerance;
  return abs(weight - total) <= tolerance;
}

void startPuzzle() {
  currentNumberOfIngredients = 1;
  setLightGreen();
  updateState(InProgress);
}

void puzzleFailed() {
  setLightRed();
  updateState(Failed);
}

void puzzleSolved() {
  updateState(Solved);
  digitalWrite(lockPin, HIGH);
  setLightOff();
}

void deactivate() {
  currentNumberOfIngredients = 0;
  setLightOff();
  updateState(Inactive);
}

bool isEmpty(float weight) {
  return weight <= EMPTY_THRESHOLD;
}

bool isFirstIngredientOnly(float weight) {
 return weightInRange(0, weight);
}

void updateState(PuzzleState newState) {
  lastStateChange = millis();
  puzzleState = newState;
}

void setLightOff() {
  setRGBColor(0, 0, 0);}

void setLightGreen() {
  setRGBColor(0, 255, 0);
}

void setLightRed() {
  setRGBColor(255, 0, 0);
}

void setRGBColor(int red, int green, int blue) {
  ledcWrite(0, red);   // Channel 0 = red
  ledcWrite(1, green); // Channel 1 = green
  ledcWrite(2, blue);  // Channel 2 = blue
}
