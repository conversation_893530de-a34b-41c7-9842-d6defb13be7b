; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:esp32-c3-supermini]
platform = espressif32
board = lolin_c3_mini
framework = arduino

; Serial monitor configuration
monitor_speed = 115200
monitor_filters = esp32_exception_decoder

; ; Upload configuration for better reliability
; upload_speed = 460800
; upload_flags =
;     --before=default_reset
;     --after=hard_reset
;     --connect-attempts=20
;     --chip=esp32c3

; Build flags to ensure proper USB CDC support
build_flags =
    -D ARDUINO_USB_CDC_ON_BOOT=1
    -D ARDUINO_USB_MODE=1